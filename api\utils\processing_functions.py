"""
This module handles the processing of various tasks related to job descriptions,
resumes, and interview experiences, including generating responses and updating
the status of tasks. This is the main file providing functions to api endpoints created.
"""

from api.prompts.prompts import get_role_initial_message, get_jd_initial_message, get_resume_initial_message
from api.database.job_status_db import insert_record, update_status, insert_status
from api.database.session_db import  get_requests_by_session, does_session_not_exist
from api.database.job_requests_db import get_role_experience_level, update_job_experience, update_jd, update_resume
import time
import json
from api.common.api_logger import api_logger as logger
from fastapi import HTTPException
from api.utils.function_utils import extract_questions_from_responses, create_interview_experience_messages, get_file_text_fastapi
from api.utils.qa_model import generate_response

def generate_response_background(session_id, job_title, experience_level, request_type, model, number_of_questions, connection):
    """
    Generates a response in the background for a given session, job title,
    and experience level. Updates the status of the task in the database.
    """
    logger.info(f"Starting background response generation for session_id: {session_id}, job_title: {job_title}")
    try:
        start_time = time.time()  # Start time for measuring response time
        session_not_exists = does_session_not_exist(session_id, "job_title", connection)
        if not session_not_exists:
            update_job_experience(session_id=session_id, connection=connection)
        else:
            logger.info("No previous session id")
        # Insert record with initial status 'Inprogress'
        insert_status(session_id, "job_title", "Inprogress", connection)
        
        res = generate_response(get_role_initial_message(job_title, experience_level), model, session_id, "job_title")

        end_time = time.time()  # End time for response time calculation
        if not res:
            logger.error(f"GPT error for session_id: {session_id} during job_title generation")
            res, total_tokens = None, None
            response_time = int(end_time - start_time)
        else:
            logger.info(f"Succesfully generated job title questions for session_id: {session_id}")    
            res, total_tokens = res
            response_time = int(end_time - start_time)  # Response time in milliseconds
        insert_record(
            session_id=session_id,
            request_type=request_type,
            gpt_response=res,
            response_time=response_time,
            model=model,
            number_of_questions=number_of_questions,
            total_tokens=total_tokens,
            role=job_title,
            experience_level=experience_level,
            connection=connection
        )
        logger.info(f"Inserted initial status for session_id: {session_id}")
        try:
            update_status(session_id, "job_title", "complete", connection)
            # print("Status Updated")
        except Exception as e:
            logger.error("Exception during Inserting background task status:", e)
            update_status(session_id, "job_title", "failed", connection)
        logger.info(f"Response generated for session_id: {session_id}, response_time: {response_time}ms")

    except Exception as e:
        logger.error(f"Exception during background task for session_id: {session_id}: {e}")
        update_status(session_id, "job_title", "failed", connection)


def process_upload_jd(session_id, text, model, connection):
    """
    Processes the upload of a job description, generates a response,
    and updates the status in the database.
    """
    logger.info(f"Processing JD upload for session_id: {session_id}")
    try:
        start_time = time.time()  # Start time for measuring response time
        session_not_exists = does_session_not_exist(session_id, "jd", connection)
        if not session_not_exists:
            update_jd(session_id=session_id, connection=connection)
        else:
            logger.info("No previous session id")

        insert_status(session_id, "jd", "Inprogress", connection)
        messages = get_jd_initial_message(text)
        res = generate_response(messages, model, session_id, "jd")

        end_time = time.time()  # End time for response time calculation
        if not res:
            logger.error(f"GPT error for session_id: {session_id} during JD generation")
            res, total_tokens = None, None
            response_time = int(end_time - start_time)
        else:
            res, total_tokens = res
            response_time = end_time - start_time  # Response time in seconds

        request_type = "jd"

        jd_text = str(text)
        # print(f"Session ID: {session_id}  , Request Type:  {request_type} , GPT Response: {gpt_response} , JD text: {jd_text}")
        # Insert record into the database
        insert_record(
            session_id=session_id,
            request_type=request_type,
            gpt_response=res,
            response_time=response_time,
            model=model,
            total_tokens=total_tokens,
            jd_text=jd_text,
            connection=connection
        )
        logger.info(f"JD response generated for session_id: {session_id}, response_time: {response_time}s")
        try:
            update_status(session_id, request_type, "complete", connection)
        except Exception as e:
            logger.error("Exception during Inserting background task progress:", e)
            update_status(session_id, request_type, "failed", connection)

    except Exception as e:
        logger.error(f"Exception during JD processing for session_id: {session_id}: {e}")
        update_status(session_id, request_type, "failed", connection)

def process_interview(session_id, model, connection):
    """
    Processes an interview session by extracting questions from responses
    and generating a response based on the role and experience level.
    """
    logger.info(f"Processing interview for session_id: {session_id}")
    # Retrieve all responses for this session, including the latest
    start_time = time.time()  # Start time for measuring response time
    all_responses = get_requests_by_session(session_id, connection)
    questions = extract_questions_from_responses(all_responses,session_id)
    # print("Extracted Questions: ", questions)

    res = get_role_experience_level(session_id, connection)
    if res:
        role = res[0]['role']
        # print("Role: ", role)
        experience_level = res[0]['experience_level']
        # print("Experience Level: ", experience_level)
        number_of_questions = res[0]['number_of_questions']
        # print("Questions: ", number_of_questions)

        logger.info(f"Role: {role}, Experience Level: {experience_level}, Number of Questions: {number_of_questions} for session_id: {session_id}")
    else:
        role = ""
        # print("Role: ", role)
        experience_level = ""
        # print("Experience Level: ", experience_level)
        number_of_questions = ""
        # print("Questions: ", number_of_questions)

    messages = create_interview_experience_messages(questions= questions, n=number_of_questions, role=role, experience_level= experience_level)
    latest_response = generate_response(messages, model, session_id, "interview_experience")
    end_time = time.time()  # End time for response time calculation

    if not latest_response:
        raise HTTPException(status_code=500, detail="GPT error")
    
    gpt_response, total_tokens = latest_response
    
    # Check if gpt_response is already a dict or needs to be parsed from a string
    if isinstance(gpt_response, dict):
        questions_json = gpt_response.get("questions", [])
        # Convert back to string for database storage
        gpt_response_str = json.dumps(gpt_response)
    else:
        # If it's a string, parse it
        gpt_response_dict = json.loads(gpt_response)
        questions_json = gpt_response_dict.get("questions", [])
        gpt_response_str = gpt_response
    
    response_time = end_time - start_time  # Response time in seconds

    request_type = "interview_experience"
    try:
        # Insert record into the database
        insert_record(session_id=session_id, request_type=request_type, gpt_response=gpt_response_str,
                      response_time=response_time, model=model, total_tokens=total_tokens, connection=connection)
    except Exception as e:
        logger.error("Exception: ", e)
    finally:
        logger.info(f"Extracted questions for session_id: {session_id}: {questions}")
        return {"session_id": session_id, "questions": questions_json, "status": True}
    logger.info(f"Interview response generated for session_id: {session_id}, response_time: {response_time}s")

def process_resume(session_id, resume_file_name, model, connection):
    """
    Processes the upload of a resume, extracts text, generates a response,
    and updates the status in the database.
    """
    logger.info(f"Processing resume for session_id: {session_id}")
    try:
        # start_time = time.time() 
        start_time = time.time()  # Start time for measuring response time
        session_not_exists = does_session_not_exist(session_id, "resume", connection)
        if not session_not_exists:
            logger.info(f"Session ID: {session_id} already exists")
            update_resume(session_id=session_id, connection=connection)
        else:
            logger.info(f"No previous session id found for session_id: {session_id}")

            
        insert_status(session_id, "resume", "Inprogress", connection)
         # Start time for measuring response time
        resume_text = get_file_text_fastapi(resume_file_name)
        if len(resume_text) < 20:
            logger.error(f"Lenght of resume text is less than 20 characters for session_id: {session_id}")
            logger.info(f"Resume text: {resume_text} for session_id: {session_id}")

            logger.error(f"Failed to extract resume text for session_id: {session_id}")
            update_status(session_id, "resume", "failed", connection)
            latest_response, total_tokens = None, None
        else:
            logger.info(f"Successfully extracted resume text for session_id: {session_id}")
            messages = get_resume_initial_message(resume_text)
            
            latest_response = generate_response(messages, model, session_id, "resume")

            if not latest_response:
                logger.error(f"GPT error for session_id: {session_id} during resume generation")
                latest_response, total_tokens = None, None
                
            else:
                logger.info(f"Succesfully generated resume questions for session_id: {session_id}")
                latest_response, total_tokens = latest_response
        end_time = time.time()  # End time for response time calculation
        response_time = int(end_time - start_time)
        request_type = "resume"
        gpt_response = latest_response  # Assuming 'latest_response' contains the GPT response
        # print(f"Session Id : {session_id} , Request Type: {request_type} , GPT Response: {gpt_response}")
        try:
            # Insert record into the database
            insert_record(
                session_id=session_id,
                request_type=request_type,
                gpt_response=gpt_response,
                response_time=response_time,
                model=model,
                total_tokens=total_tokens,
                resume_text = resume_text,
                connection=connection
            )
        except Exception as e:
            logger.error(f"Error {e} during inserting record for session_id: {session_id} during resume generation")

        try:
            if latest_response:
                update_status(session_id, request_type, "complete", connection)
        except Exception as e:
            logger.error(f"Error {e} during updating status for session_id: {session_id} during resume generation")
            update_status(session_id,request_type, "failed", connection)
        # logger.info(f"Resume text extracted for session_id: {session_id}, length: {len(resume_text)}")
        logger.info(f"Resume response generated for session_id: {session_id}, response_time: {response_time}ms")

    except Exception as e:
        logger.error(f"Exception during resume processing for session_id: {session_id}: {e}")
        update_status(session_id,request_type, "failed", connection)

