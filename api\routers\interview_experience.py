"""
This module handles the interview experience-related endpoints. It provides functionality to get interview experiences and session data.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
import time
from api.common.dependencies import get_api_key
from api.database.connection_db import get_connection
from api.database.session_db import fetch_session_data
from api.database.job_status_db import are_all_tasks_complete
from api.utils.processing_functions import process_interview
from api.common.pydantic_models import (
    GPTModels, SessionData, InterviewExperienceResponse,
    QuestionWithAudio
)
from api.utils.tts_service import tts_service
from api.database.audio_jobs_db import audio_jobs_db
from typing import List, Optional
from api.common.api_logger import api_logger as logger

router = APIRouter()

async def generate_initial_audio_background(
    questions: List[str],
    voice_id: str,
    job_id: str,
    initial_count: int,
    model: str = "eleven_flash_v2_5"
):
    """
    Background task to generate audio for initial N questions.
    """
    try:
        logger.info(f"Starting initial audio generation for {initial_count} questions, job: {job_id}")

        # Generate audio for first N questions
        for i in range(min(initial_count, len(questions))):
            success, s3_url, duration = await tts_service.generate_audio_file(
                text=questions[i],
                voice_id=voice_id,
                job_id=job_id,
                question_index=i,
                model=model
            )

            if success:
                from api.common.pydantic_models import AudioFile
                audio_file = AudioFile(
                    question_index=i,
                    question_text=questions[i],
                    file_url=s3_url,
                    duration_seconds=duration
                )
                audio_jobs_db.add_audio_file(job_id, audio_file)
                logger.info(f"Generated audio for question {i+1}/{initial_count}")
            else:
                logger.error(f"Failed to generate audio for question {i+1}: {s3_url}")

        # Update job status
        progress = int((min(initial_count, len(questions)) / len(questions)) * 100)
        audio_jobs_db.update_job_status(job_id, "processing", progress)

    except Exception as e:
        logger.error(f"Error in initial audio generation for job {job_id}: {str(e)}")
        audio_jobs_db.update_job_status(job_id, "failed", error_message=str(e))

@router.post("/get_interview_experience/", response_model=InterviewExperienceResponse)
async def get_interview_experience(
    session_id: str,
    model: GPTModels = Query(...),
    is_ca: bool = Query(False),
    image_id: Optional[str] = Query(None, description="Interviewer image ID for voice mapping"),
    initial_audio_count: int = Query(3, description="Number of questions to generate audio for immediately"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    token: bool = Depends(get_api_key)
) -> InterviewExperienceResponse:
    """
    Fetches interview experience based on session ID and model.
    Optionally generates audio for the first N questions.
    """
    try:
        logger.info(f"Request received for Interview Experience with Session ID: {session_id} Model: {model.value} and is_ca: {is_ca}")
        start_time = time.time()
        connection = get_connection(is_ca)
        model_value = model.value
        if model_value=="gpt-3.5-turbo" or model_value == "gpt-3.5-turbo-1106":
            model_value="gpt-4o-mini"
        elif model_value == "gpt-4-turbo-preview" or model_value =="gpt-4-1106-preview":
            model_value = "gpt-4o"
        logger.info(f"Processing request for Interview Experience with Session ID: {session_id} Model: {model_value} and is_ca: {is_ca}")

        while not are_all_tasks_complete(session_id, connection):
            if time.time() - start_time > 30:  # Check if 30 seconds has passed
                logger.info(f"Timeout reached, proceeding with incomplete tasks for session ID: {session_id}")
                break
            time.sleep(2)
            try:
                connection.close()
                connection = get_connection(is_ca)
            except Exception as e:
                logger.error(f"Exception: {e} Occurred while refreshing connection for session ID: {session_id}")

        logger.info(f"Proceeding with tasks completion status for session ID: {session_id}")
        result = process_interview(session_id, model_value, connection)

        if not result.get("status", False):
            return InterviewExperienceResponse(
                session_id=session_id,
                questions=[],
                status=False
            )

        questions_list = result.get("questions", [])
        questions_with_audio = []
        audio_job_id = None

        # Generate audio for first N questions synchronously if image_id is provided
        if image_id and questions_list and initial_audio_count > 0:
            try:
                # Map image_id to voice_id
                voice_id = tts_service.map_image_to_voice(image_id)
                audio_job_id = tts_service.generate_job_id()

                logger.info(f"Generating audio synchronously for first {initial_audio_count} questions")

                # Generate audio for first N questions synchronously
                for i, question in enumerate(questions_list):
                    if i < initial_audio_count:
                        # Generate audio synchronously for first N questions
                        success, s3_url, duration = await tts_service.generate_audio_file(
                            text=question,
                            voice_id=voice_id,
                            job_id=audio_job_id,
                            question_index=i,
                            model="eleven_flash_v2_5"
                        )

                        if success:
                            questions_with_audio.append(QuestionWithAudio(
                                question_id=f"q_{i+1}",
                                question=question,
                                audio_url=s3_url,
                                duration_seconds=duration
                            ))
                            logger.info(f"Generated audio for question {i+1}/{initial_audio_count}")
                        else:
                            logger.error(f"Failed to generate audio for question {i+1}: {s3_url}")
                            questions_with_audio.append(QuestionWithAudio(
                                question_id=f"q_{i+1}",
                                question=question,
                                audio_url=None,
                                duration_seconds=None
                            ))
                    else:
                        # No audio for remaining questions initially
                        questions_with_audio.append(QuestionWithAudio(
                            question_id=f"q_{i+1}",
                            question=question,
                            audio_url=None,
                            duration_seconds=None
                        ))

                # Create audio job for tracking (optional - for remaining questions)
                if len(questions_list) > initial_audio_count:
                    audio_jobs_db.create_job(
                        job_id=audio_job_id,
                        questions=questions_list[initial_audio_count:],  # Remaining questions
                        voice_id=voice_id,
                        model="eleven_flash_v2_5"
                    )

                    # Start background generation for remaining questions
                    background_tasks.add_task(
                        generate_initial_audio_background,
                        questions_list[initial_audio_count:],  # Remaining questions
                        voice_id,
                        audio_job_id,
                        len(questions_list) - initial_audio_count
                    )

                    logger.info(f"Started background audio generation for remaining {len(questions_list) - initial_audio_count} questions")

            except Exception as e:
                logger.error(f"Error generating audio: {str(e)}")
                # Fallback: create questions without audio
                for i, question in enumerate(questions_list):
                    questions_with_audio.append(QuestionWithAudio(
                        question_id=f"q_{i+1}",
                        question=question,
                        audio_url=None,
                        duration_seconds=None
                    ))
        else:
            # No audio generation - just convert questions to QuestionWithAudio format
            for i, question in enumerate(questions_list):
                questions_with_audio.append(QuestionWithAudio(
                    question_id=f"q_{i+1}",
                    question=question,
                    audio_url=None,
                    duration_seconds=None
                ))

        response = InterviewExperienceResponse(
            session_id=session_id,
            questions=questions_with_audio,
            status=True,
            audio_job_id=audio_job_id,
            initial_audio_count=initial_audio_count if image_id else None
        )

        logger.info(f"Interview Experience Request with Session ID: {session_id} Successfully processed")
        return response

    except Exception as e:
        logger.error(f"Exception: {e} Occurred while processing interview experience request for session ID: {session_id}")
        return InterviewExperienceResponse(
            session_id=session_id,
            questions=[],
            status=False
        )

@router.get("/get_session_data/{session_id}", response_model=List[SessionData])
def get_session_data(session_id: str, is_ca: bool = Query(False), token: bool = Depends(get_api_key)):
    """
    Retrieves session data for a given session ID.
    """
    logger.info(f"Fetching session data for session ID: {session_id} and is_ca: {is_ca}")
    try:
        connection = get_connection(is_ca)
        session_data = fetch_session_data(session_id, connection)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")
    except Exception as e:
        logger.error(f"Exception: {e} Occurred while fetching session data")
        session_data = []
    logger.info(f"Session data fetched successfully for session ID: {session_id}")
    return session_data