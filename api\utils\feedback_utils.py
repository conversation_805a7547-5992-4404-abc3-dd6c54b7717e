"""
Utility functions for feedback CSV processing and validation.
"""

from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile
from typing import Dict, List, Tuple, Any
from api.common.api_logger import api_logger as logger
from api.database.feedback_db import import_feedback_examples_from_csv
from api.common.pydantic_models import CsvUploadResponse
import io
import csv


def validate_csv_file_type(file: UploadFile) -> None:
    """
    Validate that the uploaded file is a CSV file.
    
    Args:
        file: The uploaded file to validate
        
    Raises:
        HTTPException: If the file is not a CSV file
    """
    if not file.filename.endswith('.csv'):
        raise HTTPException(
            status_code=400,
            detail="Only CSV files are accepted"
        )


def decode_csv_content(contents: bytes) -> str:
    """
    Decode CSV file contents and check for extra text before CSV data.
    
    Args:
        contents: Raw file contents as bytes
        
    Returns:
        Decoded string content
        
    Raises:
        HTTPException: If content cannot be decoded or contains invalid format
    """
    try:
        content_str = contents.decode('utf-8')

        # Check for extra text before CSV data
        lines = content_str.strip().split('\n')
        if lines:
            first_line = lines[0].strip()
            # Check if first line looks like it could be CSV headers
            if first_line and not any(col in first_line.lower() for col in ['question', 'answer', 'suggestions', 'proposed']):
                # Check if it looks like extra text instead of CSV
                if len(first_line.split(',')) < 3 or any(phrase in first_line.lower() for phrase in [
                    'this is', 'note:', 'instructions:', 'please', 'make sure', 'important:', 'readme'
                ]):
                    raise HTTPException(
                        status_code=400,
                        detail="Failed to parse file. The file appears to have extra text before CSV data. Make sure it's a proper CSV file starting with headers: question, answer, suggestions, proposed_answer"
                    )

        return content_str
    except UnicodeDecodeError:
        raise HTTPException(
            status_code=400,
            detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
        )
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=400,
            detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
        )


def validate_csv_structure(content_str: str) -> Dict[str, str]:
    """
    Validate CSV structure and find required columns.
    
    Args:
        content_str: Decoded CSV content as string
        
    Returns:
        Dictionary mapping required columns to actual column names found in CSV
        
    Raises:
        HTTPException: If CSV structure is invalid or required columns are missing
    """
    try:
        csv_file_for_validation = io.StringIO(content_str)
        reader = csv.DictReader(csv_file_for_validation)
        fieldnames = reader.fieldnames

        if not fieldnames:
            raise HTTPException(
                status_code=400,
                detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
            )

        # Define column mappings to handle both singular and plural forms
        column_mappings = {
            'question': ['question', 'questions'],
            'answer': ['answer', 'answers'],
            'suggestions': ['suggestions', 'suggestion'],
            'proposed_answer': ['proposed_answer', 'proposed_answers']
        }
        
        # Find actual column names from the CSV that match our requirements
        found_columns = {}
        fieldnames_lower = [fn.lower().strip() for fn in fieldnames if fn is not None]
        
        for required_col, variations in column_mappings.items():
            found = False
            for variation in variations:
                if variation in fieldnames_lower:
                    # Find the actual column name (with original case)
                    actual_col_name = next(fn for fn in fieldnames if fn.lower().strip() == variation)
                    found_columns[required_col] = actual_col_name
                    found = True
                    break

        # Check if all required columns were found
        missing_columns = []
        for required_col in column_mappings.keys():
            if required_col not in found_columns:
                missing_columns.append(required_col)

        if missing_columns:
            expected_variations = []
            for missing_col in missing_columns:
                variations = column_mappings[missing_col]
                expected_variations.append(f"{missing_col} (or {'/'.join(variations)})")
            
            raise HTTPException(
                status_code=400,
                detail=f"Failed to parse file. Missing required columns: {', '.join(expected_variations)}"
            )

        # Check for extra columns that might indicate invalid CSV format
        used_columns = set(found_columns.values())
        extra_columns = [col for col in fieldnames if col not in used_columns and col is not None and col.strip()]
        if extra_columns:
            # Check if extra columns look like they might be data instead of headers
            suspicious_extras = []
            for col in extra_columns:
                # If column name is very long or contains typical data patterns, it might be misplaced data
                if len(col) > 50 or any(phrase in col.lower() for phrase in ['i am', 'my experience', 'years', 'skills', 'worked']):
                    suspicious_extras.append(col[:50] + "..." if len(col) > 50 else col)

            if suspicious_extras:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to parse file. The file appears to have extra text or invalid CSV format. Make sure it's a proper CSV file with only the required columns: question, answer, suggestions, proposed_answer. Found suspicious content: {', '.join(suspicious_extras)}"
                )

        return found_columns

    except HTTPException:
        raise
    except Exception as e:
        # Check if the error suggests invalid CSV format
        error_str = str(e).lower()
        if any(phrase in error_str for phrase in ['delimiter', 'quote', 'escape', 'line']):
            raise HTTPException(
                status_code=400,
                detail="Failed to parse file. The file appears to have invalid CSV format or extra text. Make sure it's a proper CSV file with columns: question, answer, suggestions, proposed_answer"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="Failed to parse file. Make sure it's a CSV file and has columns: question, answer, suggestions, proposed_answer"
            )


def validate_csv_rows(content_str: str, found_columns: Dict[str, str]) -> Tuple[int, int, List[str]]:
    """
    Validate CSV rows and count valid/invalid ones.
    
    Args:
        content_str: Decoded CSV content as string
        found_columns: Dictionary mapping required columns to actual column names
        
    Returns:
        Tuple of (valid_rows_count, skipped_rows_count, skipped_details)
        
    Raises:
        HTTPException: If no valid rows are found
    """
    csv_file_for_validation = io.StringIO(content_str)
    reader = csv.DictReader(csv_file_for_validation)
    valid_rows = 0
    skipped_rows = 0
    skipped_details = []
    row_number = 1  # Start from 1 (header is row 0)

    for row in reader:
        row_number += 1

        # Check if all required fields have values (not empty or just whitespace)
        # Use the found column names instead of the standard ones
        missing_values = []
        has_any_required_data = False
        
        for required_col, actual_col in found_columns.items():
            value = row.get(actual_col, '').strip()
            if not value:
                missing_values.append(required_col)
            else:
                has_any_required_data = True

        # Skip rows that have no data in any required columns (completely empty or only irrelevant data)
        if not has_any_required_data:
            # This is an empty row or row with only irrelevant data - skip silently
            continue

        if missing_values:
            skipped_rows += 1
            if len(skipped_details) < 5:  # Store first 5 for display
                skipped_details.append(f"Row {row_number} missing: {', '.join(missing_values)}")
        else:
            valid_rows += 1

    # If no valid rows, return error
    if valid_rows == 0:
        raise HTTPException(status_code=400, detail="No valid data rows found in CSV file")

    return valid_rows, skipped_rows, skipped_details


def generate_success_message(count: int, skipped_rows: int, skipped_details: List[str]) -> str:
    """
    Generate success message with details about processing results.
    
    Args:
        count: Number of successfully imported records
        skipped_rows: Number of skipped rows
        skipped_details: List of details about skipped rows
        
    Returns:
        Formatted success message
    """
    if skipped_rows > 0:
        success_msg = f"Successfully imported {count} feedback examples from CSV. "
        success_msg += f"Skipped {skipped_rows} rows with missing values. "
        if skipped_details:
            success_msg += f"Skipped rows: {'; '.join(skipped_details)} ; "
            if skipped_rows > 5:
                success_msg += f" and {skipped_rows - 5} more..."
        success_msg += " All previous examples have been replaced."
    else:
        success_msg = f"Successfully imported {count} feedback examples from CSV. All previous examples have been replaced."
    
    return success_msg


async def process_feedback_csv_upload(file: UploadFile) -> CsvUploadResponse:
    """
    Process CSV file upload for feedback examples.
    
    This function handles the complete workflow of validating and importing
    feedback examples from a CSV file.
    
    Args:
        file: The uploaded CSV file
        
    Returns:
        CsvUploadResponse with success message and details
        
    Raises:
        HTTPException: If validation fails or import fails
    """
    try:
        # Step 1: Validate file type
        validate_csv_file_type(file)

        # Step 2: Read and decode file content
        contents = await file.read()
        content_str = decode_csv_content(contents)

        # Step 3: Validate CSV structure and find columns
        found_columns = validate_csv_structure(content_str)

        # Step 4: Validate rows and count valid/invalid ones
        valid_rows, skipped_rows, skipped_details = validate_csv_rows(content_str, found_columns)

        # Step 5: Import valid rows (skip invalid ones)
        csv_file = io.BytesIO(contents)
        success, count = import_feedback_examples_from_csv(csv_file, found_columns)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to import feedback examples from CSV"
            )

        # Step 6: Generate success message
        success_msg = generate_success_message(count, skipped_rows, skipped_details)
        
        return CsvUploadResponse(message=success_msg)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error importing feedback examples from CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 